import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { ZodSchema } from 'zod';
import { Database } from '@/types/supabase';
import { handleUnauthorized, handleZodError } from './index';

// Middleware to check if user is authenticated
export async function withAuth(
  req: NextRequest,
  handler: (req: NextRequest, session: any) => Promise<NextResponse>
): Promise<NextResponse> {
  const supabase = createRouteHandlerClient<Database>({ cookies });
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return handleUnauthorized();
  }

  return handler(req, session);
}

// Middleware to check if user is an admin
export async function withAdmin(
  req: NextRequest,
  handler: (req: NextRequest, session: any) => Promise<NextResponse>
): Promise<NextResponse> {
  const supabase = createRouteHandlerClient<Database>({ cookies });
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return handleUnauthorized();
  }

  // Check if user is an admin
  const { data: user } = await supabase
    .from('users')
    .select('role')
    .eq('id', session.user.id)
    .single();

  if (!user || user.role !== 'admin') {
    return handleUnauthorized('Admin access required');
  }

  return handler(req, session);
}

// Middleware to validate request body against a schema
export async function withValidation<T>(
  req: NextRequest,
  schema: ZodSchema<T>,
  handler: (req: NextRequest, validData: T) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const body = await req.json();
    const validData = schema.parse(body);
    return handler(req, validData);
  } catch (error) {
    if (error instanceof Error && 'format' in error) {
      return handleZodError(error as any);
    }
    return handleZodError({
      errors: [{ path: [], message: 'Invalid request body' }]
    } as any);
  }
}

// Middleware to validate query parameters against a schema
export async function withQueryValidation<T>(
  req: NextRequest,
  schema: ZodSchema<T>,
  handler: (req: NextRequest, validData: T) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const url = new URL(req.url);
    const queryParams: Record<string, any> = {};
    
    // Convert query parameters to appropriate types
    url.searchParams.forEach((value, key) => {
      // Try to convert to number if possible
      if (!isNaN(Number(value))) {
        queryParams[key] = Number(value);
      } 
      // Convert to boolean if 'true' or 'false'
      else if (value === 'true') {
        queryParams[key] = true;
      } 
      else if (value === 'false') {
        queryParams[key] = false;
      } 
      // Otherwise keep as string
      else {
        queryParams[key] = value;
      }
    });

    const validData = schema.parse(queryParams);
    return handler(req, validData);
  } catch (error) {
    if (error instanceof Error && 'format' in error) {
      return handleZodError(error as any);
    }
    return handleZodError({
      errors: [{ path: [], message: 'Invalid query parameters' }]
    } as any);
  }
}

// Combine multiple middleware functions
export function compose(...middlewares: any[]) {
  return async (req: NextRequest, handler: any) => {
    let result = handler;
    
    // Apply middlewares in reverse order
    for (let i = middlewares.length - 1; i >= 0; i--) {
      const middleware = middlewares[i];
      const nextHandler = result;
      result = async (req: NextRequest, ...args: any[]) => 
        middleware(req, (...newArgs: any[]) => nextHandler(req, ...newArgs));
    }
    
    return result(req);
  };
}
