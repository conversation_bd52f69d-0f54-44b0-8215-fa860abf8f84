'use client';

import { CartProvider } from '@/lib/context/cart-context';
import { CheckoutProvider } from '@/lib/context/checkout-context';
import { CartDrawer } from '@/components/cart/cart-drawer';

export function AppProviders({ children }: { children: React.ReactNode }) {
  return (
    <CartProvider>
      <CheckoutProvider>
        {children}
        {/* Global cart drawer - always available */}
        <CartDrawer />
      </CheckoutProvider>
    </CartProvider>
  );
}
